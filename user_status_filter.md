# UserListPaginatedView - user_status_filter Parameter Examples

The `UserListPaginatedView` now supports an optional `user_status_filter` query parameter that allows Admin users to have granular control over which users are returned based on their `is_active` status.

## Usage Examples

### For Admin Users (is_staff=True or is_superuser=True)

#### 1. Get all users (both active and inactive)
```
GET /api/users/paginated/?user_status_filter=all
```

#### 2. Get only active users
```
GET /api/users/paginated/?user_status_filter=active
```

#### 3. Get only inactive users
```
GET /api/users/paginated/?user_status_filter=inactive
```

#### 4. Default behavior (no parameter or invalid value)
```
GET /api/users/paginated/
GET /api/users/paginated/?user_status_filter=invalid_value
```
Both return all users (same as `user_status_filter=all`)

### For Non-Admin Users

The `user_status_filter` parameter is **ignored** for non-admin users. They will always see only active users regardless of the parameter value:

```
GET /api/users/paginated/?user_status_filter=all        # Returns only active users
GET /api/users/paginated/?user_status_filter=inactive   # Returns only active users  
GET /api/users/paginated/                               # Returns only active users
```

## Parameter Values

| Value | Description | Admin Only |
|-------|-------------|------------|
| `all` | Return all users regardless of is_active status | ✅ |
| `active` | Return only active users (is_active=True) | ✅ |
| `inactive` | Return only inactive users (is_active=False) | ✅ |
| *empty/invalid* | Default behavior (Admin: all users, Non-admin: active only) | - |

## Combining with Other Filters

The `user_status_filter` works alongside all existing filters:

```
GET /api/users/paginated/?user_status_filter=inactive&role=agent&search=john
```

This would return inactive users with the 'agent' role whose name contains 'john' (Admin only).

## Security Notes

- Only Admin users (is_staff=True or is_superuser=True) can use this parameter
- Non-admin users are restricted to active users only for security
- Invalid parameter values default to the standard behavior
- The parameter is case-insensitive (`ACTIVE`, `active`, `Active` all work)
